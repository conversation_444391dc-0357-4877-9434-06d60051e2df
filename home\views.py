# from rest_framework.views import APIView
# from rest_framework.response import Response
# from rest_framework import status
# from .models import Note
# from .serializers import NoteSerializer
# from rest_framework.permissions import AllowAny
# import json 

# class NoteListCreateView(APIView):

#     permission_classes = [AllowAny]

#     def get(self, request):
#         notes = Note.objects.all()
#         serializer = NoteSerializer(notes, many=True)
#         return Response(serializer.data)

#     def post(self, request):

#         serializer = NoteSerializer(data=request.data)

#         if serializer.is_valid():
#             serializer.save()
#             print(json.dumps(serializer.data, indent=4))
#             return Response(serializer.data, status=status.HTTP_201_CREATED)
#         return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)
    
#     def delete(self, request):

#         Note.objects.all().delete()
#         return Response({"message":"All notes are deleted"}, status=status.HTTP_204_NO_CONTENT)




# notes/views.py
# from rest_framework.views import APIView
# from rest_framework.response import Response
# from rest_framework import status, permissions
# from django.contrib.auth.models import User
# from .serializers import RegisterSerializer, NoteSerializer
# from .models import Note
# from django.core.mail import send_mail
# from django.utils.encoding import force_bytes, force_str
# from django.utils.http import urlsafe_base64_encode, urlsafe_base64_decode
# from django.contrib.auth.tokens import default_token_generator
# from rest_framework_simplejwt.tokens import RefreshToken
# from django.urls import reverse
# from django.conf import settings

# Registration with email verification
# class RegisterView(APIView):
#     permission_classes = [permissions.AllowAny]

#     def post(self, request):
#         serializer = RegisterSerializer(data=request.data)
#         if serializer.is_valid():
#             user = serializer.save()
#             uid = urlsafe_base64_encode(force_bytes(user.pk))
#             token = default_token_generator.make_token(user)
#             # Send email (console backend for dev)
#             activation_link = f"http://localhost:8000/activate/{uid}/{token}/"
#             send_mail(
#                 subject="Activate your account",
#                 message=f"Click to activate: {activation_link}",
#                 from_email=settings.DEFAULT_FROM_EMAIL,
#                 recipient_list=[user.email],
#                 fail_silently=False
#             )
#             return Response({"message": "User created. Check email to activate."}, status=status.HTTP_201_CREATED)
#         return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

# # Email Activation
# class ActivateView(APIView):
#     permission_classes = [permissions.AllowAny]

#     def get(self, request, uidb64, token):
#         try:
#             uid = force_str(urlsafe_base64_decode(uidb64))
#             user = User.objects.get(pk=uid)
#         except (TypeError, ValueError, OverflowError, User.DoesNotExist):
#             return Response({"error": "Invalid link"}, status=status.HTTP_400_BAD_REQUEST)

#         if default_token_generator.check_token(user, token):
#             user.is_active = True
#             user.save()
#             return Response({"message": "Account activated. You can login now."})
#         return Response({"error": "Invalid or expired token"}, status=status.HTTP_400_BAD_REQUEST)

# Login with JWT
# class LoginView(APIView):
#     permission_classes = [permissions.AllowAny]

#     def post(self, request):
#         username = request.data.get("username")
#         password = request.data.get("password")
#         user = User.objects.filter(username=username).first()
#         if user and user.check_password(password):
#             if not user.is_active:
#                 return Response({"error": "Account not activated"}, status=status.HTTP_401_UNAUTHORIZED)
#             refresh = RefreshToken.for_user(user)
#             return Response({
#                 "refresh": str(refresh),
#                 "access": str(refresh.access_token),
#             })
#         return Response({"error": "Invalid credentials"}, status=status.HTTP_401_UNAUTHORIZED)

# # Notes CRUD
# class NoteListCreateView(APIView):
#     permission_classes = [permissions.IsAuthenticated]

#     def get(self, request):
#         notes = request.user.notes.all()
#         serializer = NoteSerializer(notes, many=True)
#         return Response(serializer.data)

#     def post(self, request):
#         serializer = NoteSerializer(data=request.data)
#         if serializer.is_valid():
#             serializer.save(user=request.user)
#             return Response(serializer.data, status=status.HTTP_201_CREATED)
#         return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

# class NoteDetailView(APIView):
#     permission_classes = [permissions.IsAuthenticated]

#     def get_object(self, pk, user):
#         try:
#             return Note.objects.get(pk=pk, user=user)
#         except Note.DoesNotExist:
#             return None

#     def delete(self, request, pk):
#         note = self.get_object(pk, request.user)
#         if not note:
#             return Response({"error": "Note not found"}, status=status.HTTP_404_NOT_FOUND)
#         note.delete()
#         return Response({"message": "Note deleted"}, status=status.HTTP_200_OK)




from rest_framework.views import APIView
from rest_framework.permissions import AllowAny
# from .serializers import RegistrationSerializer
from rest_framework.response import Response
from rest_framework import status



# class RegistrationView(APIView):
#     permission_classes=[AllowAny]

#     def post(self, request):
#         serializer = RegistrationSerializer(data=request.data)
#         if serializer.is_valid():
#             user = serializer.save()
#             return Response(user, status=status.HTTP_201_CREATED)
#         return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)