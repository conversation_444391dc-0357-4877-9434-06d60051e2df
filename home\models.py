# from django.db import models
# from django.contrib.auth.models import User

# class Task(models.Model):
#     # Choices for priority levels
#     PRIORITY_CHOICES = [
#         ('low', 'Low'),
#         ('medium', 'Medium'),
#         ('high', 'High'),
#     ]
    
#     title = models.CharField(max_length=200)  # Short text field
#     description = models.TextField(blank=True)  # Long text, optional
#     completed = models.BooleanField(default=False)  # True/False
#     priority = models.CharField(
#         max_length=10, 
#         choices=PRIORITY_CHOICES, 
#         default='medium'
#     )
#     created_at = models.DateTimeField(auto_now_add=True)  # Set once
#     updated_at = models.DateTimeField(auto_now=True)  # Updates every save
#     owner = models.ForeignKey(User, on_delete=models.CASCADE)  # Links to user
    
#     class Meta:
#         ordering = ['-created_at']  # Newest first
    
#     def __str__(self):
#         return self.title  # How object appears in admin/debugging



# from django.db import models
# from django.contrib.auth.models import User


# class Note(models.model):
#     user = models.ForeignKey(User, on_delete=models.CASCADE, related_name='notes')
#     title = models.CharField(max_length=200)
#     content = models.TextField()
#     created_at = models.DateTimeField(auto_now_add=True)

#     def __str__(self):
#         return self.title


from django.db import models
# from django.contrib.auth.models import User


class Note(models.Model):
    # user = models.ForeignKey(User, on_delete=models.CASCADE, related_name="notes")
    title = models.CharField(max_length=200)
    content = models.TextField()
    created_at = models.DateTimeField(auto_now_add=True)

    def __str__(self):
        return self.title