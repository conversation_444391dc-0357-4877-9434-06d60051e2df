# from rest_framework import serializers
# from .models import Task

# class TaskSerializer(serializers.ModelSerializer):
#     owner = serializers.StringRelatedField(read_only=True)  # Show username, not ID
    
#     class Meta:
#         model = Task
#         fields = ['id', 'title', 'description', 'completed', 'priority', 
#                  'created_at', 'updated_at', 'owner']
#         read_only_fields = ['id', 'created_at', 'updated_at', 'owner']

# # Alternative: Manual serializer (more control)
# class TaskDetailSerializer(serializers.ModelSerializer):
#     days_since_created = serializers.SerializerMethodField()
    
#     class Meta:
#         model = Task
#         fields = ['id', 'title', 'description', 'completed', 'priority', 
#                  'created_at', 'updated_at', 'owner', 'days_since_created']
    
#     def get_days_since_created(self, obj):
#         from django.utils import timezone
#         return (timezone.now() - obj.created_at).days

# from rest_framework import serializers
# from django.contrib.auth.password_validation import validate_password
# from django.contrib.auth.models import User
# from .models import Note


# class RegisterSerializer(serializers.ModelSerializer):

#     password = serializers.CharField(write_only=True, validators=[validate_password])

#     class Meta:
#         model = User
#         fields = ['username', 'email', 'password']

#     def create(self, validated_data):
#         user = User.objects.create_user(
#             username=validated_data["username"],
#             email=validated_data['email'],
#             password=validated_data['password'],
#             is_active=False
#         )
        
#         return user
    
# class NoteSerializer(serializers.ModelSerializer):
#     class Meta:
#         model = Note
#         fields = ["id", 'title', 'conent', 'created_at']



from rest_framework import serializers
from .models import Note
# from django.contrib.auth.models import User
from django.contrib.auth.password_validation import validate_password

class NoteSerializer(serializers.ModelSerializer):

    class Meta:
        model = Note
        fields = ['title', 'content', 'created_at']


# class RegistrationSerializer(serializers.ModelSerializer):

#     password = serializers.CharField(write_only=True, validators = [validate_password])

#     class Meta:
#         model = User
#         fields = ['fullname', 'email', 'password']

#     def create(self, validated_data):
#         user = User.objects.create_user(
#             email=validated_data['email'],
#             username=validated_data['username'],
#             password=validated_data['password']
#         )

#         return user



# Things to remember creating an serializer that some serializers can belong to an model and some do not belong to any model . a serializer defination starts with the meta class then what model it belongs to then the fields itll have then there are custom functions a serilizer can have. we can also put password verifications on the top also. 