from django.urls import path
from . import views


urlpatterns = [
    # path('', views.RegistrationView.as_view(), name='user-register')
]


# urlpatterns = [
#     path('', views.home, name='home'),
#     path('notes/', views.api_view, name='note-list-create'),
# ]

# app_name = 'tasks'  # Namespace for URL names

# urlpatterns = [
#     # Class-based views
#     path('', views.TaskListCreateView.as_view(), name='task-list-create'),
#     path('<int:pk>/', views.TaskDetailView.as_view(), name='task-detail'),
    
#     # Function-based views
#     path('<int:task_id>/complete/', views.mark_task_complete, name='task-complete'),
#     path('statistics/', views.task_statistics, name='task-statistics'),
# ]

